{"__meta": {"id": "X5dc59e1412709121be9117026f5670fc", "datetime": "2025-08-14 13:28:14", "utime": 1755152894.088639, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1755152893.843879, "end": 1755152894.088663, "duration": 0.24478411674499512, "duration_str": "245ms", "measures": [{"label": "Booting", "start": 1755152893.843879, "relative_start": 0, "end": 1755152894.031782, "relative_end": 1755152894.031782, "duration": 0.18790292739868164, "duration_str": "188ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1755152894.031797, "relative_start": 0.18791794776916504, "end": 1755152894.088665, "relative_end": 1.9073486328125e-06, "duration": 0.05686807632446289, "duration_str": "56.87ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 18832168, "peak_usage_str": "18MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "auth.login", "param_count": null, "params": [], "start": 1755152894.077835, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}}, {"name": "layouts.restaurant", "param_count": null, "params": [], "start": 1755152894.080838, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/layouts/restaurant.blade.phplayouts.restaurant", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Flayouts%2Frestaurant.blade.php&line=1", "ajax": false, "filename": "restaurant.blade.php", "line": "?"}}]}, "route": {"uri": "GET login", "middleware": "web, guest, Closure", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@create", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=18\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:18-21</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "IX4eeRzHVAg2fDHcVCKcCYviHDYT5eo7NDYwVkKu", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/login\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-95335959 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-95335959\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-560592881 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-560592881\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1269813710 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1269813710\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-535179565 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en,th-TH;q=0.9,th;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImZIUG0rbmtETFdlMXRNZUpqWDZibVE9PSIsInZhbHVlIjoiMmFRa2NhMVR3aHpxUmkyUnJURy9DakpIRmhmek14UUtrZzVyUk5xQlgweEpyazEvem4zMzkyVXpwUUcvOGdjbG1tWmdhWWFGV0JBdndiUGEveGVHTDNBdnUxaEF2QWt1dVRXVHZzZ2tQYTlESi9XdTlNYzdCcUpIdzhGMEVzRi8iLCJtYWMiOiIzMzczNmVjMmU3ZDRmM2EzZGQ2Mzk0ZmNmYWFjODE4NTI4M2ZkNTE3OWRjMWFjN2JkM2Y5MzY1ZGM2ZjUzMzg3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkdLQWhkWGs1L01qaGJieGhLWWJVNFE9PSIsInZhbHVlIjoidTV0d1JaUnI1MmdqMGl4YWg4SGcwbnhMYlBuazQwUFFRYTI1Nk0rWkM3bGx4d3Z3dFhMdVVkaUJmQ1ViODNEUlVIbk1Qb1p3dVZRRUpIVDdhVG5DaXg5cDdZb0E3N1VaVFBoNDE5K1lMblNwYmNoWjV1L3FyY0kyN29VNUtqWCsiLCJtYWMiOiI1NjNhZDNmOGU5Yzc1ZTNlZTI5NDUzZDliZDAzODI4YzFkZjJkZTliZTE2MjQyNWJmYWMwYjE3ZTA0ODI3YWNlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-535179565\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1975473531 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IX4eeRzHVAg2fDHcVCKcCYviHDYT5eo7NDYwVkKu</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">mIkmxUXO4MeCzvEjt89VeiKqFEJj0DW1BXD7bRDW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1975473531\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1724212751 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 14 Aug 2025 06:28:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlMxSERscVRHWllucjlqUUFCNHdxVHc9PSIsInZhbHVlIjoic0dEdVp4Skh6UDFGVE02TlA3Si90T0ZJRy9BaTNoM2VpbTBuTHArUXEveUVoTWFsVExGUlIwSTU0MmJ3d1dGZ0VBZFgzbVZNck5Ec0pHV1JMSDVOVUE4STJHK0kwQUJaSElPVStrckhQRnZ3bUdzTkViVnlUMlMvM1Z1L1piR0EiLCJtYWMiOiI2OTJkY2E0NjMxYjhjMjFhMGNjOTYxNjExODVhZTZhMTVkZmJkMGYxYTUzY2VhYjY4MjY0ZmM2YTAxNjM3N2VhIiwidGFnIjoiIn0%3D; expires=Thu, 14 Aug 2025 08:28:14 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImlkdE1Na0VCcWhyT0pxSFFvcVMxOEE9PSIsInZhbHVlIjoiOFpNS3Ryd0RDK01ZYWJoMGU2RDg5RXBJTnc4RklSUmp6Z0grWGE2OW5Sa0hCbnk2bmVZbys3VnhoR1JCSFh0UStTTi9zZHp4Y21acnJrdlFvUmE0dG5RUk9YL0gyclRRbWFzYi9HYzQ1eTlDeUpUaEIxdUJiZldmazRYWE90NjciLCJtYWMiOiI5MjExNWVjMmRjYWE3OGNlYzEwZDg0ZGQ4NWQ5OTFmYjViYTc2YzQ4NjY3ODVmNWU5NTQyNzg5YjFiNzVlYjcwIiwidGFnIjoiIn0%3D; expires=Thu, 14 Aug 2025 08:28:14 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlMxSERscVRHWllucjlqUUFCNHdxVHc9PSIsInZhbHVlIjoic0dEdVp4Skh6UDFGVE02TlA3Si90T0ZJRy9BaTNoM2VpbTBuTHArUXEveUVoTWFsVExGUlIwSTU0MmJ3d1dGZ0VBZFgzbVZNck5Ec0pHV1JMSDVOVUE4STJHK0kwQUJaSElPVStrckhQRnZ3bUdzTkViVnlUMlMvM1Z1L1piR0EiLCJtYWMiOiI2OTJkY2E0NjMxYjhjMjFhMGNjOTYxNjExODVhZTZhMTVkZmJkMGYxYTUzY2VhYjY4MjY0ZmM2YTAxNjM3N2VhIiwidGFnIjoiIn0%3D; expires=Thu, 14-Aug-2025 08:28:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImlkdE1Na0VCcWhyT0pxSFFvcVMxOEE9PSIsInZhbHVlIjoiOFpNS3Ryd0RDK01ZYWJoMGU2RDg5RXBJTnc4RklSUmp6Z0grWGE2OW5Sa0hCbnk2bmVZbys3VnhoR1JCSFh0UStTTi9zZHp4Y21acnJrdlFvUmE0dG5RUk9YL0gyclRRbWFzYi9HYzQ1eTlDeUpUaEIxdUJiZldmazRYWE90NjciLCJtYWMiOiI5MjExNWVjMmRjYWE3OGNlYzEwZDg0ZGQ4NWQ5OTFmYjViYTc2YzQ4NjY3ODVmNWU5NTQyNzg5YjFiNzVlYjcwIiwidGFnIjoiIn0%3D; expires=Thu, 14-Aug-2025 08:28:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1724212751\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-8595032 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IX4eeRzHVAg2fDHcVCKcCYviHDYT5eo7NDYwVkKu</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-8595032\", {\"maxDepth\":0})</script>\n"}}