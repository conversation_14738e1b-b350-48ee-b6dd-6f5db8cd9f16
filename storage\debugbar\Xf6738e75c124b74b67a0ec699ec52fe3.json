{"__meta": {"id": "Xf6738e75c124b74b67a0ec699ec52fe3", "datetime": "2025-08-14 13:27:55", "utime": **********.807035, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.232319, "end": **********.807091, "duration": 0.5747718811035156, "duration_str": "575ms", "measures": [{"label": "Booting", "start": **********.232319, "relative_start": 0, "end": **********.460265, "relative_end": **********.460265, "duration": 0.22794580459594727, "duration_str": "228ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.46028, "relative_start": 0.22796082496643066, "end": **********.807099, "relative_end": 8.106231689453125e-06, "duration": 0.3468191623687744, "duration_str": "347ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 21767104, "peak_usage_str": "21MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "index", "param_count": null, "params": [], "start": **********.616062, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.phpindex", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "middleware": "web, Closure", "controller": "App\\Http\\Controllers\\IndexController@index", "namespace": null, "prefix": "", "where": [], "as": "index2", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=22\" onclick=\"\">app/Http/Controllers/IndexController.php:22-56</a>"}, "queries": {"nb_statements": 43, "nb_failed_statements": 0, "accumulated_duration": 0.13361000000000003, "accumulated_duration_str": "134ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 62}, {"index": 8, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 26}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.5113, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "IndexController.php:62", "source": "app/Http/Controllers/IndexController.php:62", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=62", "ajax": false, "filename": "IndexController.php", "line": "62"}, "connection": "shopping671", "start_percent": 0, "width_percent": 0}, {"sql": "SHOW TABLES LIKE 'reviews'", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 62}, {"index": 11, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 26}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.511822, "duration": 0.00271, "duration_str": "2.71ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:62", "source": "app/Http/Controllers/IndexController.php:62", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=62", "ajax": false, "filename": "IndexController.php", "line": "62"}, "connection": "shopping671", "start_percent": 0, "width_percent": 2.028}, {"sql": "SHOW COLUMNS FROM reviews", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 99}, {"index": 11, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 87}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 26}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.517401, "duration": 0.02009, "duration_str": "20.09ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:99", "source": "app/Http/Controllers/IndexController.php:99", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=99", "ajax": false, "filename": "IndexController.php", "line": "99"}, "connection": "shopping671", "start_percent": 2.028, "width_percent": 15.036}, {"sql": "select * from `categories`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 29}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.5564759, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "IndexController.php:29", "source": "app/Http/Controllers/IndexController.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=29", "ajax": false, "filename": "IndexController.php", "line": "29"}, "connection": "shopping671", "start_percent": 17.065, "width_percent": 0.442}, {"sql": "select * from `products`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 30}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.561639, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "IndexController.php:30", "source": "app/Http/Controllers/IndexController.php:30", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=30", "ajax": false, "filename": "IndexController.php", "line": "30"}, "connection": "shopping671", "start_percent": 17.506, "width_percent": 0.726}, {"sql": "select count(*) as aggregate from `products`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 31}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.565161, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "IndexController.php:31", "source": "app/Http/Controllers/IndexController.php:31", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=31", "ajax": false, "filename": "IndexController.php", "line": "31"}, "connection": "shopping671", "start_percent": 18.232, "width_percent": 0.374}, {"sql": "select * from `categories`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 33}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.567815, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "IndexController.php:33", "source": "app/Http/Controllers/IndexController.php:33", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=33", "ajax": false, "filename": "IndexController.php", "line": "33"}, "connection": "shopping671", "start_percent": 18.606, "width_percent": 0.374}, {"sql": "select * from `products` where `products`.`category_id` in (13, 14, 15)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 33}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.572963, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "IndexController.php:33", "source": "app/Http/Controllers/IndexController.php:33", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=33", "ajax": false, "filename": "IndexController.php", "line": "33"}, "connection": "shopping671", "start_percent": 18.981, "width_percent": 0.539}, {"sql": "select * from `event_news` order by `created_at` desc limit 6", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 36}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.576756, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "IndexController.php:36", "source": "app/Http/Controllers/IndexController.php:36", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=36", "ajax": false, "filename": "IndexController.php", "line": "36"}, "connection": "shopping671", "start_percent": 19.519, "width_percent": 0.427}, {"sql": "select * from `products` order by RAND() limit 10", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 42}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.580069, "duration": 0.01274, "duration_str": "12.74ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:42", "source": "app/Http/Controllers/IndexController.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=42", "ajax": false, "filename": "IndexController.php", "line": "42"}, "connection": "shopping671", "start_percent": 19.946, "width_percent": 9.535}, {"sql": "select * from `categories` where `categories`.`id` in (13, 14)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 42}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.596587, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "IndexController.php:42", "source": "app/Http/Controllers/IndexController.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=42", "ajax": false, "filename": "IndexController.php", "line": "42"}, "connection": "shopping671", "start_percent": 29.481, "width_percent": 0.397}, {"sql": "select * from `brands` where `brands`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 42}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.601527, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:42", "source": "app/Http/Controllers/IndexController.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=42", "ajax": false, "filename": "IndexController.php", "line": "42"}, "connection": "shopping671", "start_percent": 29.878, "width_percent": 1.602}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.62902, "duration": 0.01228, "duration_str": "12.28ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 31.48, "width_percent": 9.191}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 56 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["56", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.644686, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Product.php:57", "source": "app/Models/Product.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=57", "ajax": false, "filename": "Product.php", "line": "57"}, "connection": "shopping671", "start_percent": 40.671, "width_percent": 0.449}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.647333, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 41.12, "width_percent": 2.956}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 43 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["43", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.653293, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Product.php:57", "source": "app/Models/Product.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=57", "ajax": false, "filename": "Product.php", "line": "57"}, "connection": "shopping671", "start_percent": 44.076, "width_percent": 0.322}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.655465, "duration": 0.00426, "duration_str": "4.26ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 44.398, "width_percent": 3.188}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 40 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["40", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.662616, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Product.php:57", "source": "app/Models/Product.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=57", "ajax": false, "filename": "Product.php", "line": "57"}, "connection": "shopping671", "start_percent": 47.586, "width_percent": 0.494}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.665319, "duration": 0.0068200000000000005, "duration_str": "6.82ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 48.08, "width_percent": 5.104}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 19 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["19", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.674247, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Product.php:57", "source": "app/Models/Product.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=57", "ajax": false, "filename": "Product.php", "line": "57"}, "connection": "shopping671", "start_percent": 53.185, "width_percent": 0.374}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 49}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.6765258, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 53.559, "width_percent": 2.814}, {"sql": "select avg(`rating`) as aggregate from `reviews` where `reviews`.`product_id` = 19 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["19", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 49}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.682922, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Product.php:49", "source": "app/Models/Product.php:49", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=49", "ajax": false, "filename": "Product.php", "line": "49"}, "connection": "shopping671", "start_percent": 56.373, "width_percent": 0.397}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 49}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.6851969, "duration": 0.00321, "duration_str": "3.21ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 56.77, "width_percent": 2.403}, {"sql": "select avg(`rating`) as aggregate from `reviews` where `reviews`.`product_id` = 19 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["19", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 49}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.690727, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Product.php:49", "source": "app/Models/Product.php:49", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=49", "ajax": false, "filename": "Product.php", "line": "49"}, "connection": "shopping671", "start_percent": 59.172, "width_percent": 0.457}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 49}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.693746, "duration": 0.00325, "duration_str": "3.25ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 59.629, "width_percent": 2.432}, {"sql": "select avg(`rating`) as aggregate from `reviews` where `reviews`.`product_id` = 19 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["19", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 49}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.699393, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Product.php:49", "source": "app/Models/Product.php:49", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=49", "ajax": false, "filename": "Product.php", "line": "49"}, "connection": "shopping671", "start_percent": 62.061, "width_percent": 0.472}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 49}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.7017, "duration": 0.00311, "duration_str": "3.11ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 62.533, "width_percent": 2.328}, {"sql": "select avg(`rating`) as aggregate from `reviews` where `reviews`.`product_id` = 19 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["19", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 49}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.706928, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Product.php:49", "source": "app/Models/Product.php:49", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=49", "ajax": false, "filename": "Product.php", "line": "49"}, "connection": "shopping671", "start_percent": 64.86, "width_percent": 0.404}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 49}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.709502, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 65.265, "width_percent": 2.732}, {"sql": "select avg(`rating`) as aggregate from `reviews` where `reviews`.`product_id` = 19 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["19", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 49}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.71524, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Product.php:49", "source": "app/Models/Product.php:49", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=49", "ajax": false, "filename": "Product.php", "line": "49"}, "connection": "shopping671", "start_percent": 67.996, "width_percent": 0.367}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 660}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.717695, "duration": 0.005809999999999999, "duration_str": "5.81ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 68.363, "width_percent": 4.348}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 19 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["19", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 660}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.725934, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Product.php:57", "source": "app/Models/Product.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=57", "ajax": false, "filename": "Product.php", "line": "57"}, "connection": "shopping671", "start_percent": 72.712, "width_percent": 0.427}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.728528, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 73.138, "width_percent": 2.717}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 28 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["28", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.7348511, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "Product.php:57", "source": "app/Models/Product.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=57", "ajax": false, "filename": "Product.php", "line": "57"}, "connection": "shopping671", "start_percent": 75.855, "width_percent": 1.28}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.738902, "duration": 0.00417, "duration_str": "4.17ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 77.135, "width_percent": 3.121}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 34 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["34", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.745554, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Product.php:57", "source": "app/Models/Product.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=57", "ajax": false, "filename": "Product.php", "line": "57"}, "connection": "shopping671", "start_percent": 80.256, "width_percent": 0.374}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.7481809, "duration": 0.00991, "duration_str": "9.91ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 80.63, "width_percent": 7.417}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 29 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["29", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.761107, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Product.php:57", "source": "app/Models/Product.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=57", "ajax": false, "filename": "Product.php", "line": "57"}, "connection": "shopping671", "start_percent": 88.047, "width_percent": 0.449}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.763944, "duration": 0.00508, "duration_str": "5.08ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 88.496, "width_percent": 3.802}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 21 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["21", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.771193, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Product.php:57", "source": "app/Models/Product.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=57", "ajax": false, "filename": "Product.php", "line": "57"}, "connection": "shopping671", "start_percent": 92.298, "width_percent": 0.359}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.773485, "duration": 0.00436, "duration_str": "4.36ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 92.658, "width_percent": 3.263}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 26 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["26", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.7809691, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Product.php:57", "source": "app/Models/Product.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=57", "ajax": false, "filename": "Product.php", "line": "57"}, "connection": "shopping671", "start_percent": 95.921, "width_percent": 0.606}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.784043, "duration": 0.00417, "duration_str": "4.17ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 96.527, "width_percent": 3.121}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 24 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["24", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.790281, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Product.php:57", "source": "app/Models/Product.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=57", "ajax": false, "filename": "Product.php", "line": "57"}, "connection": "shopping671", "start_percent": 99.648, "width_percent": 0.352}]}, "models": {"data": {"App\\Models\\Product": {"value": 92, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\Category": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\EventNews": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FEventNews.php&line=1", "ajax": false, "filename": "EventNews.php", "line": "?"}}, "App\\Models\\Brand": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}}, "count": 107, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "IX4eeRzHVAg2fDHcVCKcCYviHDYT5eo7NDYwVkKu", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-547988578 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-547988578\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-570184885 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-570184885\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1877821665 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1877821665\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-993379408 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en,th-TH;q=0.9,th;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ik5LTTBxRWU0QzFKcWQyY05FazcrOVE9PSIsInZhbHVlIjoic0RCNzdRQXc5eUhSc3c3QUxubG1odDBEbVVLdVdpcUlQbThDZzhueW1EbzM4TEk2L3ZvVHZNTGV0cWNWYkFodjVraUs1eWJTYVdLSVNYR1BtSTN3QjRDbkpwWmZDc1JqUnk2SS9xYzNsZko0dlRSbS9heHgxWGhTOUd0Slg3TkIiLCJtYWMiOiI5YmViYzg1ODk0Y2U2NjQxMWVmMGFhZDMxNTkyZWYxZTcwZjg3ODBlNThiNDE5MGRlMDQ0MzYxYTIzZTE1NGI3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImN6am96bkN2TCtxUVZNK0w2aVVoeXc9PSIsInZhbHVlIjoiRUh4ZVZpSzFKekRFY2JseCtraTFZOGhtVDkydHhnRjFNbCtmVjBBa21nSzhRN0pzQmVQL3NLdFNRcjNQTkpsY2o5eXdrb2t3U05yL1cxeC8vc3d5czZ3L0FuR0lRYStkZ09mNWFKMjlNckd5WHcyME53akNVWHFEWU5OUHpLV1UiLCJtYWMiOiJhNjJjODk2YWFkNzE1MTJmOTVhYzA3N2E5ZDM0ODNiOTg5YjNkZWZkOTA2OGU1OWNhN2M0MzEyMmZjMmIzMzY1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-993379408\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1522046747 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IX4eeRzHVAg2fDHcVCKcCYviHDYT5eo7NDYwVkKu</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">mIkmxUXO4MeCzvEjt89VeiKqFEJj0DW1BXD7bRDW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1522046747\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2094216073 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 14 Aug 2025 06:27:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IldFRHVUaFhyK0h5NEVnanZIUnZSMVE9PSIsInZhbHVlIjoiQnN4ZCt3WVBocVRUbHp2dU5NM2wwZis0WVVNcHlmV0JzQlNSU1lqc3prRXZ3M1RlMVJsSFk3Qmk3eEhFNWJKVUZYaWZpMk1sR2U3WXI1YngrQ2hMQzBSSEVLYkxta1BBbmZXT0x4SEY2RHBYSHRmWVR1WDZrdHpWQlNoYitld3AiLCJtYWMiOiJkOTg5OGQ1MTFhMGJiNzY1YmQ0MGU5MWEzZDNlYWQwOTZlZWMwNDYxMTdlZWM4ZmYxOThiZTYxMDg3MGQ2MDdlIiwidGFnIjoiIn0%3D; expires=Thu, 14 Aug 2025 08:27:55 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImVVRmpwT3lqWWViUmZ0TXhRQWNCN0E9PSIsInZhbHVlIjoiVTduU21IbzBPUnRkQ3Irb1p3SkxhSGVlTzZlNm8wV21rT2JaMGZnUmM5K3g2ZjgwdWM2eGpGOWFTV1FlRzU1N0VsdFFmWWIvdGRnZ2w1UmI5OWlXZ3B1R0cxdHFRV0k0bGxQUDBlZWRRV3FlMTRDck1wd1ExYytXNDZqWWhvcXgiLCJtYWMiOiIyNzM2ODY5MWExYTExOTU2ZDI3NjQzZTY1YzE5OWZjYzY0NTcwYzU0NTI1MmQ0ZGRhOGQwNDg1ZjNjZDNhZDc2IiwidGFnIjoiIn0%3D; expires=Thu, 14 Aug 2025 08:27:55 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IldFRHVUaFhyK0h5NEVnanZIUnZSMVE9PSIsInZhbHVlIjoiQnN4ZCt3WVBocVRUbHp2dU5NM2wwZis0WVVNcHlmV0JzQlNSU1lqc3prRXZ3M1RlMVJsSFk3Qmk3eEhFNWJKVUZYaWZpMk1sR2U3WXI1YngrQ2hMQzBSSEVLYkxta1BBbmZXT0x4SEY2RHBYSHRmWVR1WDZrdHpWQlNoYitld3AiLCJtYWMiOiJkOTg5OGQ1MTFhMGJiNzY1YmQ0MGU5MWEzZDNlYWQwOTZlZWMwNDYxMTdlZWM4ZmYxOThiZTYxMDg3MGQ2MDdlIiwidGFnIjoiIn0%3D; expires=Thu, 14-Aug-2025 08:27:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImVVRmpwT3lqWWViUmZ0TXhRQWNCN0E9PSIsInZhbHVlIjoiVTduU21IbzBPUnRkQ3Irb1p3SkxhSGVlTzZlNm8wV21rT2JaMGZnUmM5K3g2ZjgwdWM2eGpGOWFTV1FlRzU1N0VsdFFmWWIvdGRnZ2w1UmI5OWlXZ3B1R0cxdHFRV0k0bGxQUDBlZWRRV3FlMTRDck1wd1ExYytXNDZqWWhvcXgiLCJtYWMiOiIyNzM2ODY5MWExYTExOTU2ZDI3NjQzZTY1YzE5OWZjYzY0NTcwYzU0NTI1MmQ0ZGRhOGQwNDg1ZjNjZDNhZDc2IiwidGFnIjoiIn0%3D; expires=Thu, 14-Aug-2025 08:27:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2094216073\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-813349596 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IX4eeRzHVAg2fDHcVCKcCYviHDYT5eo7NDYwVkKu</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-813349596\", {\"maxDepth\":0})</script>\n"}}