<?php $__env->startSection('content'); ?>
<div class="page-content">
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                    <h4 class="mb-sm-0 font-size-18">เพิ่มหมวดหมู่อาหารใหม่</h4>

                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('category.index')); ?>">หมวดหมู่</a></li>
                            <li class="breadcrumb-item active">เพิ่มหมวดหมู่ใหม่</li>
                        </ol>
                    </div>

                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-body">
                        <h4 class="card-title mb-4">
                            <i class="ri-add-circle-line me-2"></i>เพิ่มหมวดหมู่อาหารใหม่
                        </h4>

                        <?php if($errors->any()): ?>
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li><?php echo e($error); ?></li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <form action="<?php echo e(route('category.store')); ?>" method="POST" enctype="multipart/form-data">
                            <?php echo csrf_field(); ?>

                            <div class="mb-3 row">
                                <label for="name" class="col-sm-3 col-form-label">ชื่อหมวดหมู่ <span class="text-danger">*</span></label>
                                <div class="col-sm-9">
                                    <input type="text" name="name" id="name" class="form-control"
                                           value="<?php echo e(old('name')); ?>"
                                           placeholder="เช่น อาหารไทย, เครื่องดื่ม, ของหวาน" required>
                                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger mt-1"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="mb-3 row">
                                <label for="description" class="col-sm-3 col-form-label">คำอธิบาย</label>
                                <div class="col-sm-9">
                                    <textarea name="description" id="description" class="form-control" rows="4"
                                              placeholder="กรอกคำอธิบายหมวดหมู่อาหาร"><?php echo e(old('description')); ?></textarea>
                                    <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger mt-1"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="mb-3 row">
                                <label for="image_path" class="col-sm-3 col-form-label">รูปภาพหมวดหมู่</label>
                                <div class="col-sm-9">
                                    <input type="file" name="image_path" id="image_path" class="form-control"
                                           accept="image/*" onchange="previewImage(this)">
                                    <small class="text-muted">รองรับไฟล์: JPG, JPEG, PNG, GIF (ขนาดไม่เกิน 2MB)</small>
                                    <?php $__errorArgs = ['image_path'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="text-danger mt-1"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="mb-4 row">
                                <div class="col-sm-3"></div>
                                <div class="col-sm-9">
                                    <div class="d-flex gap-3">
                                        <button type="submit" class="btn btn-success waves-effect waves-light">
                                            <i class="ri-save-line me-2"></i>บันทึกข้อมูล
                                        </button>
                                        <a href="<?php echo e(route('category.index')); ?>" class="btn btn-secondary waves-effect">
                                            <i class="ri-arrow-left-line me-2"></i>ยกเลิก
                                        </a>
                                    </div>
                                </div>
                            </div>

                        </form>

                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title mb-3">
                            <i class="ri-information-line me-2"></i>คำแนะนำ
                        </h5>

                        <div class="alert alert-info">
                            <h6 class="alert-heading">💡 เคล็ดลับการเพิ่มหมวดหมู่</h6>
                            <ul class="mb-0 small">
                                <li>ใช้ชื่อหมวดหมู่ที่เข้าใจง่าย</li>
                                <li>เขียนคำอธิบายที่ชัดเจน</li>
                                <li>เลือกรูปภาพที่เหมาะสม</li>
                                <li>ตรวจสอบความถูกต้องก่อนบันทึก</li>
                            </ul>
                        </div>

                        <!-- แสดงตัวอย่างรูปภาพ -->
                        <div id="imagePreview" style="display: none;">
                            <h6 class="mb-2">
                                <i class="ri-eye-line me-2"></i>ตัวอย่างรูปภาพ
                            </h6>
                            <div class="text-center">
                                <img id="preview" src="" alt="Preview"
                                     class="img-fluid rounded border"
                                     style="max-width: 100%; max-height: 200px;">
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

<script>
function previewImage(input) {
    const preview = document.getElementById('preview');
    const previewContainer = document.getElementById('imagePreview');

    if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = function(e) {
            preview.src = e.target.result;
            previewContainer.style.display = 'block';
        }

        reader.readAsDataURL(input.files[0]);
    } else {
        previewContainer.style.display = 'none';
    }
}
</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.admin_master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\shopping67\resources\views/admin/categoriesadd.blade.php ENDPATH**/ ?>