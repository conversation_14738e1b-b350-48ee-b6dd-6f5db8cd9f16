{"__meta": {"id": "X1824c92830a3159cba798dfda63b40c8", "datetime": "2025-08-14 13:30:32", "utime": 1755153032.085576, "method": "GET", "uri": "/brand/1/edit", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.711391, "end": 1755153032.085602, "duration": 0.37421107292175293, "duration_str": "374ms", "measures": [{"label": "Booting", "start": **********.711391, "relative_start": 0, "end": **********.875709, "relative_end": **********.875709, "duration": 0.16431808471679688, "duration_str": "164ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.87573, "relative_start": 0.1643390655517578, "end": 1755153032.085605, "relative_end": 2.86102294921875e-06, "duration": 0.20987486839294434, "duration_str": "210ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 20954728, "peak_usage_str": "20MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 6, "templates": [{"name": "admin.brands.edit_brand", "param_count": null, "params": [], "start": **********.952544, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/admin/brands/edit_brand.blade.phpadmin.brands.edit_brand", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fbrands%2Fedit_brand.blade.php&line=1", "ajax": false, "filename": "edit_brand.blade.php", "line": "?"}}, {"name": "admin.admin_master", "param_count": null, "params": [], "start": 1755153032.065914, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/admin/admin_master.blade.phpadmin.admin_master", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fadmin_master.blade.php&line=1", "ajax": false, "filename": "admin_master.blade.php", "line": "?"}}, {"name": "sweetalert::alert", "param_count": null, "params": [], "start": 1755153032.067341, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/vendor/sweetalert/alert.blade.phpsweetalert::alert", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fvendor%2Fsweetalert%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}}, {"name": "admin.body.header", "param_count": null, "params": [], "start": 1755153032.068333, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/admin/body/header.blade.phpadmin.body.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fbody%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "admin.body.slidemenu", "param_count": null, "params": [], "start": 1755153032.072454, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/admin/body/slidemenu.blade.phpadmin.body.slidemenu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fbody%2Fslidemenu.blade.php&line=1", "ajax": false, "filename": "slidemenu.blade.php", "line": "?"}}, {"name": "admin.body.footer", "param_count": null, "params": [], "start": 1755153032.073621, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/admin/body/footer.blade.phpadmin.body.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fbody%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET brand/{brand}/edit", "middleware": "web, auth, admin, Closure", "controller": "App\\Http\\Controllers\\BrandController@edit", "namespace": null, "prefix": "", "where": [], "as": "brand.edit", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FBrandController.php&line=87\" onclick=\"\">app/Http/Controllers/BrandController.php:87-90</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00471, "accumulated_duration_str": "4.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 15, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.919721, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "shopping671", "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.932978, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "shopping671", "start_percent": 0, "width_percent": 71.762}, {"sql": "select * from `brands` where `id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 947}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 44}], "start": **********.9415112, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:61", "source": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=61", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "61"}, "connection": "shopping671", "start_percent": 71.762, "width_percent": 13.8}, {"sql": "select * from `users` where `users`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "admin.body.header", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/admin/body/header.blade.php", "line": 32}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": 1755153032.069122, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "admin.body.header:32", "source": "view::admin.body.header:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fbody%2Fheader.blade.php&line=32", "ajax": false, "filename": "header.blade.php", "line": "32"}, "connection": "shopping671", "start_percent": 85.563, "width_percent": 14.437}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Brand": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "I2Z3Vz3xluQ63a2C2hMQ319YZoVohQYC46c3D2Ar", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/brand/1/edit\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/brand/1/edit", "status_code": "<pre class=sf-dump id=sf-dump-1987254149 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1987254149\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1901272913 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1901272913\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1336130129 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1336130129\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1757809544 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/brand/all</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en,th-TH;q=0.9,th;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InhicUNSdklRNnorOWRYaHpzRlBlMUE9PSIsInZhbHVlIjoiaTM1UGE1OEN3UnUyeVl1RzNXK1hKcXBlZ0ZqODNwaWsrUm1WOUw5NlBpL1JrQ1cralRrTU4yNUxKK3M4ak9YeGlBK2xhVGYwbUZkb3JySkRoZGdodmlQbU1LanVYNzFiVEwvTHhHUFpyTVVSVDhKUm1pYUo2bFlaUkhLSWUwVTAiLCJtYWMiOiJmMGU1MTJjNzdkMTcyN2NjMjAyOGE5YjBlZjc5YTEyN2JmYWUxODQ0MWRhZGYzYzZlZGZlMmQ4ZWRjNTIxYzgyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InNOa2NJWW1HbDlTS0NDclVDQ0FXbUE9PSIsInZhbHVlIjoiQVJ2cWFYaVZWc0EvVFdRSmtqejJrZmt4dnppbE5ScURMWHdjekxqWXRsYllybmRmNGZ3VDg4Z0xGN25KNUkyZWZxQ1FmMzM3Q0NLYVl0NnRVYy9rMkxxcStRTnN1YzlrVVM0WTY0RFgxaGJyNi9ha0hFL3ViSTAvNytEUjFmQnAiLCJtYWMiOiIxZjNkNGZlZDY4ZTk3YTM5NDE2MDdiZWIxODIyODM5Y2I4ZDRlZTFlZTAyOTJjMmFmNTQxNGVmZTk0NzMwMDZmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1757809544\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1848750941 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I2Z3Vz3xluQ63a2C2hMQ319YZoVohQYC46c3D2Ar</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ChTsjbIyZRcG6kRqcd2vjnKzNyQaAU2IaFy0VSoa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1848750941\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1367784075 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 14 Aug 2025 06:30:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkhKcHVsVUR0bjl3TnlSdjQwVVBrc1E9PSIsInZhbHVlIjoib1J5YmJXd0Z0Y3pBczNONXMvM0xYcFNOVno2RTZPSitVUDdWWVVQMG5JMElUSkpMbVR3RE1wcWFDNHFUbnErMkh3MWlpSnFtdFhmbzlxMEh2dWhkdExrR0hBYjh3WHRVOElUTXZjcE1FMVN0VUo1aUNrY0R2aEFJa2w4OGJsNDgiLCJtYWMiOiJhYzcxOGU0ODhmOGVmYTdlYjNmMWUyZGRlMTZhZDAwZTIzMmRhNmNkOTkxNjkwMmJkODg4NmQwMzliZWM4YmYxIiwidGFnIjoiIn0%3D; expires=Thu, 14 Aug 2025 08:30:32 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImpIOVZPT1pyRGNoTkNzQmplVGRaUlE9PSIsInZhbHVlIjoiM3lTZTluUmdkeENFa0YwclNWanVFa3kvQU1ubDBveGdxQzVSVjJHTzRYclZPcmV4OEhhdzZVc09COFNad1RMc0NqQjNuM0x0SWtDSEt6OTU0S2FkNG8yeGpoOFc2b0pYZ3gxaHFiNGFYYVZhdnA2YllLcDV2WERpcEcxbitNdDgiLCJtYWMiOiIxYWJjZmQ5MTgyNGMwNjhhYjY4ZTE2OGRmZTQxNDE4MzI3N2FlMzk2MWRmMzE3YTBmOTU5ZjM4OTMwNDkxMTJhIiwidGFnIjoiIn0%3D; expires=Thu, 14 Aug 2025 08:30:32 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkhKcHVsVUR0bjl3TnlSdjQwVVBrc1E9PSIsInZhbHVlIjoib1J5YmJXd0Z0Y3pBczNONXMvM0xYcFNOVno2RTZPSitVUDdWWVVQMG5JMElUSkpMbVR3RE1wcWFDNHFUbnErMkh3MWlpSnFtdFhmbzlxMEh2dWhkdExrR0hBYjh3WHRVOElUTXZjcE1FMVN0VUo1aUNrY0R2aEFJa2w4OGJsNDgiLCJtYWMiOiJhYzcxOGU0ODhmOGVmYTdlYjNmMWUyZGRlMTZhZDAwZTIzMmRhNmNkOTkxNjkwMmJkODg4NmQwMzliZWM4YmYxIiwidGFnIjoiIn0%3D; expires=Thu, 14-Aug-2025 08:30:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImpIOVZPT1pyRGNoTkNzQmplVGRaUlE9PSIsInZhbHVlIjoiM3lTZTluUmdkeENFa0YwclNWanVFa3kvQU1ubDBveGdxQzVSVjJHTzRYclZPcmV4OEhhdzZVc09COFNad1RMc0NqQjNuM0x0SWtDSEt6OTU0S2FkNG8yeGpoOFc2b0pYZ3gxaHFiNGFYYVZhdnA2YllLcDV2WERpcEcxbitNdDgiLCJtYWMiOiIxYWJjZmQ5MTgyNGMwNjhhYjY4ZTE2OGRmZTQxNDE4MzI3N2FlMzk2MWRmMzE3YTBmOTU5ZjM4OTMwNDkxMTJhIiwidGFnIjoiIn0%3D; expires=Thu, 14-Aug-2025 08:30:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1367784075\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1387280063 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I2Z3Vz3xluQ63a2C2hMQ319YZoVohQYC46c3D2Ar</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/brand/1/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1387280063\", {\"maxDepth\":0})</script>\n"}}