{"__meta": {"id": "Xf36ab3c398e3ae57b5f252fd078322f0", "datetime": "2025-08-14 13:27:59", "utime": 1755152879.11773, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1755152878.508694, "end": 1755152879.117763, "duration": 0.6090691089630127, "duration_str": "609ms", "measures": [{"label": "Booting", "start": 1755152878.508694, "relative_start": 0, "end": 1755152878.667045, "relative_end": 1755152878.667045, "duration": 0.15835118293762207, "duration_str": "158ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1755152878.667074, "relative_start": 0.15838003158569336, "end": 1755152879.117767, "relative_end": 4.0531158447265625e-06, "duration": 0.45069313049316406, "duration_str": "451ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 18826688, "peak_usage_str": "18MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "auth.login", "param_count": null, "params": [], "start": 1755152878.736482, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}}, {"name": "layouts.restaurant", "param_count": null, "params": [], "start": 1755152878.967745, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/layouts/restaurant.blade.phplayouts.restaurant", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Flayouts%2Frestaurant.blade.php&line=1", "ajax": false, "filename": "restaurant.blade.php", "line": "?"}}]}, "route": {"uri": "GET login", "middleware": "web, guest, Closure", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@create", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=18\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:18-21</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "IX4eeRzHVAg2fDHcVCKcCYviHDYT5eo7NDYwVkKu", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/login\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-966362398 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-966362398\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1907774558 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1907774558\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1913825027 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1913825027\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1898603244 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en,th-TH;q=0.9,th;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IldFRHVUaFhyK0h5NEVnanZIUnZSMVE9PSIsInZhbHVlIjoiQnN4ZCt3WVBocVRUbHp2dU5NM2wwZis0WVVNcHlmV0JzQlNSU1lqc3prRXZ3M1RlMVJsSFk3Qmk3eEhFNWJKVUZYaWZpMk1sR2U3WXI1YngrQ2hMQzBSSEVLYkxta1BBbmZXT0x4SEY2RHBYSHRmWVR1WDZrdHpWQlNoYitld3AiLCJtYWMiOiJkOTg5OGQ1MTFhMGJiNzY1YmQ0MGU5MWEzZDNlYWQwOTZlZWMwNDYxMTdlZWM4ZmYxOThiZTYxMDg3MGQ2MDdlIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImVVRmpwT3lqWWViUmZ0TXhRQWNCN0E9PSIsInZhbHVlIjoiVTduU21IbzBPUnRkQ3Irb1p3SkxhSGVlTzZlNm8wV21rT2JaMGZnUmM5K3g2ZjgwdWM2eGpGOWFTV1FlRzU1N0VsdFFmWWIvdGRnZ2w1UmI5OWlXZ3B1R0cxdHFRV0k0bGxQUDBlZWRRV3FlMTRDck1wd1ExYytXNDZqWWhvcXgiLCJtYWMiOiIyNzM2ODY5MWExYTExOTU2ZDI3NjQzZTY1YzE5OWZjYzY0NTcwYzU0NTI1MmQ0ZGRhOGQwNDg1ZjNjZDNhZDc2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1898603244\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-602589200 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IX4eeRzHVAg2fDHcVCKcCYviHDYT5eo7NDYwVkKu</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">mIkmxUXO4MeCzvEjt89VeiKqFEJj0DW1BXD7bRDW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-602589200\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1074954605 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 14 Aug 2025 06:27:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjlMQ1VYUkJRY2M1bzdTWXJrMlZ5dmc9PSIsInZhbHVlIjoiY202M0lRK2Zvc21rZXlsNDFSUGVxYThpaFVKV3J4YmNkYWQyZXZEckkydlg4TEY3aWdSc2JUWEpYSHBCMTJCOEhlbTBJUXY1MHJsSThDTTZTWVF4a3B2UUJRQWgxOUplQVZrZFBkays4ditlc3BwRHBGanIvV240dGpIRjVCVWciLCJtYWMiOiJmZTY0ODQ2MTA0N2I2OGYwZWRmMWIyNzEyNjY3MWViNzg5YmU4ZTQ4NjE1ODY0YTNmZTJjMTVkNTlhZTQwYjMyIiwidGFnIjoiIn0%3D; expires=Thu, 14 Aug 2025 08:27:59 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6Im9VSHBNYXVJa210S21uK1RJMWxPUmc9PSIsInZhbHVlIjoidmZNRzFRcDBKeTJIQzNuUTFZQ2ZrS2F6Qy9RY1pEalFqcGpsTWJYNlRSMWFqcDV6cGhGck5Gb3BwY2FuK01JbTRFTisya0U3anNGQzJOazBZdS9yWUlNZ1F0SUp2Y0pmYTFxUVZCcHJkblpPRWF3MTQ1SzM2RlQrdlNoQmQvdU8iLCJtYWMiOiI2YWVlNWNlYWQzZDYzYWRkNzM4YzM0Y2FiMjY5MzJmNTFkZDdiN2IzODRjZWUxZjhkZjc3Y2JhMjZiZThhZjNhIiwidGFnIjoiIn0%3D; expires=Thu, 14 Aug 2025 08:27:59 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjlMQ1VYUkJRY2M1bzdTWXJrMlZ5dmc9PSIsInZhbHVlIjoiY202M0lRK2Zvc21rZXlsNDFSUGVxYThpaFVKV3J4YmNkYWQyZXZEckkydlg4TEY3aWdSc2JUWEpYSHBCMTJCOEhlbTBJUXY1MHJsSThDTTZTWVF4a3B2UUJRQWgxOUplQVZrZFBkays4ditlc3BwRHBGanIvV240dGpIRjVCVWciLCJtYWMiOiJmZTY0ODQ2MTA0N2I2OGYwZWRmMWIyNzEyNjY3MWViNzg5YmU4ZTQ4NjE1ODY0YTNmZTJjMTVkNTlhZTQwYjMyIiwidGFnIjoiIn0%3D; expires=Thu, 14-Aug-2025 08:27:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6Im9VSHBNYXVJa210S21uK1RJMWxPUmc9PSIsInZhbHVlIjoidmZNRzFRcDBKeTJIQzNuUTFZQ2ZrS2F6Qy9RY1pEalFqcGpsTWJYNlRSMWFqcDV6cGhGck5Gb3BwY2FuK01JbTRFTisya0U3anNGQzJOazBZdS9yWUlNZ1F0SUp2Y0pmYTFxUVZCcHJkblpPRWF3MTQ1SzM2RlQrdlNoQmQvdU8iLCJtYWMiOiI2YWVlNWNlYWQzZDYzYWRkNzM4YzM0Y2FiMjY5MzJmNTFkZDdiN2IzODRjZWUxZjhkZjc3Y2JhMjZiZThhZjNhIiwidGFnIjoiIn0%3D; expires=Thu, 14-Aug-2025 08:27:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1074954605\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-453281032 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IX4eeRzHVAg2fDHcVCKcCYviHDYT5eo7NDYwVkKu</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-453281032\", {\"maxDepth\":0})</script>\n"}}