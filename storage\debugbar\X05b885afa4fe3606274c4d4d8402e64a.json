{"__meta": {"id": "X05b885afa4fe3606274c4d4d8402e64a", "datetime": "2025-08-14 13:28:21", "utime": 1755152901.331642, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1755152901.069453, "end": 1755152901.331669, "duration": 0.26221609115600586, "duration_str": "262ms", "measures": [{"label": "Booting", "start": 1755152901.069453, "relative_start": 0, "end": 1755152901.253449, "relative_end": 1755152901.253449, "duration": 0.18399596214294434, "duration_str": "184ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1755152901.253466, "relative_start": 0.18401288986206055, "end": 1755152901.331671, "relative_end": 1.9073486328125e-06, "duration": 0.07820510864257812, "duration_str": "78.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 18832168, "peak_usage_str": "18MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "auth.login", "param_count": null, "params": [], "start": 1755152901.320892, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}}, {"name": "layouts.restaurant", "param_count": null, "params": [], "start": 1755152901.323869, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/layouts/restaurant.blade.phplayouts.restaurant", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Flayouts%2Frestaurant.blade.php&line=1", "ajax": false, "filename": "restaurant.blade.php", "line": "?"}}]}, "route": {"uri": "GET login", "middleware": "web, guest, Closure", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@create", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=18\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:18-21</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "IX4eeRzHVAg2fDHcVCKcCYviHDYT5eo7NDYwVkKu", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/login\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1281955209 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1281955209\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1325773136 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1325773136\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1040533354 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1040533354\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1125505436 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en,th-TH;q=0.9,th;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ims4YUZqUEZPNHI1dmNNWHptVGIxd3c9PSIsInZhbHVlIjoiOTZ1OUFrRGtKWm1GL0JSd2JaZVcrSTNZaldsOWpUV3lnYmpOWEpwL1NzQmpnOW83ZVp0SlFmSkhWT1BHOG1wMnUyenkrd1ZSVjZ2dXBHS1ZwaXFaQkk3VXhpVmV0T0ZMSjdvazJqdFIya01pakNtV0JCcmw4RVFzRXVOTDN6RHMiLCJtYWMiOiI4ZGUwN2U2MWNkZWMzZjAxNTJjYjg3MjQ1NGRjZGMxNTJmMjRmYmZiMWNhN2EwNDFlMmRhOTc5OTMxNWU1NzM3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InRreEVBVzA0VWZNUkdmRVZxUmpqbGc9PSIsInZhbHVlIjoiMHBjb3R1N3dkaUwzdzRyVkJ2ZkczMGpaNVpyTnFXcHFCS2c1T0d1NjM5M2FYYlVPNU52cW03NlVLZEp1M0VHQWJrU3JpdjhQQ3RraW94UEJ4Qzk1Q2tXcVBvR0NycTBVbVBRa2R6L2ViSXFtQ2c4cG9rTXRaSmF3Y2FHaElUR2kiLCJtYWMiOiI4N2Q1NDFmZWFmYzQ1ZjUwYzhkNzViMWQ2MzllODNkYjc5ZWE3MTA3MzNkZDU4MWQyM2RhMjAwNTAzYmJlNmNlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1125505436\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2037320838 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IX4eeRzHVAg2fDHcVCKcCYviHDYT5eo7NDYwVkKu</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">mIkmxUXO4MeCzvEjt89VeiKqFEJj0DW1BXD7bRDW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2037320838\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1075384276 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 14 Aug 2025 06:28:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImxDZEpPOTZwSEdQS3pYa2xiMGQ2Rmc9PSIsInZhbHVlIjoicXdqclNOaUE5aklXRmRwc3FMNEorTm8weFFVS2lKcTVENllQbHlFcDgrSDZHSGRnQUxuaDNIeGh2SG1TYjl0RGRjRXJuTFNtZHNMRSt2USt0MHFUUEFEVU81WFNaTmFvblh0MFdPcDc3cFFha1FuZ0FPdW0vWFR4U2ZZaklsYysiLCJtYWMiOiIwZWEwYmEzMjU1YTliMTUwMDEwMDMyYmU0OWE0YWQ4NjY3ODhhYjk2NWFjODRlMzIwZjczMGFkMzcwY2IwN2I1IiwidGFnIjoiIn0%3D; expires=Thu, 14 Aug 2025 08:28:21 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkcwQ2hDM09Bd0o2bHJaVEVVWE13VVE9PSIsInZhbHVlIjoic09JRjEwckRXREVEYXJlekRId1QrRHg1VmUwanJJMlhQa1NXdXlOOW1VUFlSMnk5ZERKSXQ0UG8ra0Y3Z1Z2Rnpudng5LzlPc25NZGlaNDAwRUNNdTdLaU45RnI3SnlrS1Y4WmtjQ1JuVk04bDJ4eS9jbFBVajJ0TFRhTWpXMmgiLCJtYWMiOiI5OGM5NmQ4YjBjMGNkNDM3ODZjYWZiMGRhMDgxOWFmZTRkMWUzNzRiOWExMWM1MTAwNWIyNDc3Mjg0Yjk2MDZiIiwidGFnIjoiIn0%3D; expires=Thu, 14 Aug 2025 08:28:21 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImxDZEpPOTZwSEdQS3pYa2xiMGQ2Rmc9PSIsInZhbHVlIjoicXdqclNOaUE5aklXRmRwc3FMNEorTm8weFFVS2lKcTVENllQbHlFcDgrSDZHSGRnQUxuaDNIeGh2SG1TYjl0RGRjRXJuTFNtZHNMRSt2USt0MHFUUEFEVU81WFNaTmFvblh0MFdPcDc3cFFha1FuZ0FPdW0vWFR4U2ZZaklsYysiLCJtYWMiOiIwZWEwYmEzMjU1YTliMTUwMDEwMDMyYmU0OWE0YWQ4NjY3ODhhYjk2NWFjODRlMzIwZjczMGFkMzcwY2IwN2I1IiwidGFnIjoiIn0%3D; expires=Thu, 14-Aug-2025 08:28:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkcwQ2hDM09Bd0o2bHJaVEVVWE13VVE9PSIsInZhbHVlIjoic09JRjEwckRXREVEYXJlekRId1QrRHg1VmUwanJJMlhQa1NXdXlOOW1VUFlSMnk5ZERKSXQ0UG8ra0Y3Z1Z2Rnpudng5LzlPc25NZGlaNDAwRUNNdTdLaU45RnI3SnlrS1Y4WmtjQ1JuVk04bDJ4eS9jbFBVajJ0TFRhTWpXMmgiLCJtYWMiOiI5OGM5NmQ4YjBjMGNkNDM3ODZjYWZiMGRhMDgxOWFmZTRkMWUzNzRiOWExMWM1MTAwNWIyNDc3Mjg0Yjk2MDZiIiwidGFnIjoiIn0%3D; expires=Thu, 14-Aug-2025 08:28:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1075384276\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1759437569 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IX4eeRzHVAg2fDHcVCKcCYviHDYT5eo7NDYwVkKu</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1759437569\", {\"maxDepth\":0})</script>\n"}}