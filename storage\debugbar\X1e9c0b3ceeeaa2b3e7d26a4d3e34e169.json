{"__meta": {"id": "X1e9c0b3ceeeaa2b3e7d26a4d3e34e169", "datetime": "2025-08-14 13:27:41", "utime": 1755152861.691573, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1755152848.31553, "end": 1755152861.691625, "duration": 13.376095056533813, "duration_str": "13.38s", "measures": [{"label": "Booting", "start": 1755152848.31553, "relative_start": 0, "end": 1755152850.180883, "relative_end": 1755152850.180883, "duration": 1.8653528690338135, "duration_str": "1.87s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1755152850.181382, "relative_start": 1.865851879119873, "end": 1755152861.691631, "relative_end": 5.9604644775390625e-06, "duration": 11.510249137878418, "duration_str": "11.51s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 22001936, "peak_usage_str": "21MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "index", "param_count": null, "params": [], "start": 1755152859.118146, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.phpindex", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "middleware": "web, Closure", "controller": "App\\Http\\Controllers\\IndexController@index", "namespace": null, "prefix": "", "where": [], "as": "index2", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=22\" onclick=\"\">app/Http/Controllers/IndexController.php:22-56</a>"}, "queries": {"nb_statements": 56, "nb_failed_statements": 0, "accumulated_duration": 0.5955400000000001, "accumulated_duration_str": "596ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 62}, {"index": 8, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 26}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.605262, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "IndexController.php:62", "source": "app/Http/Controllers/IndexController.php:62", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=62", "ajax": false, "filename": "IndexController.php", "line": "62"}, "connection": "shopping671", "start_percent": 0, "width_percent": 0}, {"sql": "SHOW TABLES LIKE 'reviews'", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 62}, {"index": 11, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 26}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.688902, "duration": 0.23296, "duration_str": "233ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:62", "source": "app/Http/Controllers/IndexController.php:62", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=62", "ajax": false, "filename": "IndexController.php", "line": "62"}, "connection": "shopping671", "start_percent": 0, "width_percent": 39.117}, {"sql": "SHOW COLUMNS FROM reviews", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 99}, {"index": 11, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 87}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 26}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.934858, "duration": 0.05216, "duration_str": "52.16ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:99", "source": "app/Http/Controllers/IndexController.php:99", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=99", "ajax": false, "filename": "IndexController.php", "line": "99"}, "connection": "shopping671", "start_percent": 39.117, "width_percent": 8.758}, {"sql": "select * from `categories`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 29}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.305578, "duration": 0.07135, "duration_str": "71.35ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:29", "source": "app/Http/Controllers/IndexController.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=29", "ajax": false, "filename": "IndexController.php", "line": "29"}, "connection": "shopping671", "start_percent": 47.876, "width_percent": 11.981}, {"sql": "select * from `products`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 30}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.5116038, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:30", "source": "app/Http/Controllers/IndexController.php:30", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=30", "ajax": false, "filename": "IndexController.php", "line": "30"}, "connection": "shopping671", "start_percent": 59.857, "width_percent": 0.631}, {"sql": "select count(*) as aggregate from `products`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 31}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.520562, "duration": 0.00744, "duration_str": "7.44ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:31", "source": "app/Http/Controllers/IndexController.php:31", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=31", "ajax": false, "filename": "IndexController.php", "line": "31"}, "connection": "shopping671", "start_percent": 60.488, "width_percent": 1.249}, {"sql": "select * from `categories`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 33}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.5309958, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "IndexController.php:33", "source": "app/Http/Controllers/IndexController.php:33", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=33", "ajax": false, "filename": "IndexController.php", "line": "33"}, "connection": "shopping671", "start_percent": 61.737, "width_percent": 0.102}, {"sql": "select * from `products` where `products`.`category_id` in (13, 14, 15)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 33}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.696691, "duration": 0.0077599999999999995, "duration_str": "7.76ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:33", "source": "app/Http/Controllers/IndexController.php:33", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=33", "ajax": false, "filename": "IndexController.php", "line": "33"}, "connection": "shopping671", "start_percent": 61.84, "width_percent": 1.303}, {"sql": "select * from `event_news` order by `created_at` desc limit 6", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 36}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.7477999, "duration": 0.02149, "duration_str": "21.49ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:36", "source": "app/Http/Controllers/IndexController.php:36", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=36", "ajax": false, "filename": "IndexController.php", "line": "36"}, "connection": "shopping671", "start_percent": 63.143, "width_percent": 3.608}, {"sql": "select * from `products` order by RAND() limit 10", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 42}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.773019, "duration": 0.02314, "duration_str": "23.14ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:42", "source": "app/Http/Controllers/IndexController.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=42", "ajax": false, "filename": "IndexController.php", "line": "42"}, "connection": "shopping671", "start_percent": 66.751, "width_percent": 3.886}, {"sql": "select * from `categories` where `categories`.`id` in (13, 14)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 42}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.959195, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:42", "source": "app/Http/Controllers/IndexController.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=42", "ajax": false, "filename": "IndexController.php", "line": "42"}, "connection": "shopping671", "start_percent": 70.637, "width_percent": 0.208}, {"sql": "select * from `brands` where `brands`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/IndexController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\IndexController.php", "line": 42}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.99645, "duration": 0.00667, "duration_str": "6.67ms", "memory": 0, "memory_str": null, "filename": "IndexController.php:42", "source": "app/Http/Controllers/IndexController.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FIndexController.php&line=42", "ajax": false, "filename": "IndexController.php", "line": "42"}, "connection": "shopping671", "start_percent": 70.845, "width_percent": 1.12}, {"sql": "select * from `users` where `id` = '4' limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 75}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 196}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 167}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 491}], "start": **********.6980588, "duration": 0.00934, "duration_str": "9.34ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:75", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:75", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=75", "ajax": false, "filename": "EloquentUserProvider.php", "line": "75"}, "connection": "shopping671", "start_percent": 71.965, "width_percent": 1.568}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755152861.0222058, "duration": 0.02271, "duration_str": "22.71ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 73.533, "width_percent": 3.813}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 42 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["42", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1755152861.084333, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Product.php:57", "source": "app/Models/Product.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=57", "ajax": false, "filename": "Product.php", "line": "57"}, "connection": "shopping671", "start_percent": 77.347, "width_percent": 0.188}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755152861.087707, "duration": 0.00578, "duration_str": "5.78ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 77.535, "width_percent": 0.971}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 22 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["22", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1755152861.096292, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "Product.php:57", "source": "app/Models/Product.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=57", "ajax": false, "filename": "Product.php", "line": "57"}, "connection": "shopping671", "start_percent": 78.505, "width_percent": 0.225}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 49}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755152861.100075, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 78.73, "width_percent": 0.665}, {"sql": "select avg(`rating`) as aggregate from `reviews` where `reviews`.`product_id` = 22 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["22", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 49}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1755152861.106437, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Product.php:49", "source": "app/Models/Product.php:49", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=49", "ajax": false, "filename": "Product.php", "line": "49"}, "connection": "shopping671", "start_percent": 79.395, "width_percent": 0.092}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 49}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755152861.109225, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 79.488, "width_percent": 0.667}, {"sql": "select avg(`rating`) as aggregate from `reviews` where `reviews`.`product_id` = 22 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["22", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 49}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1755152861.1162908, "duration": 0.00307, "duration_str": "3.07ms", "memory": 0, "memory_str": null, "filename": "Product.php:49", "source": "app/Models/Product.php:49", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=49", "ajax": false, "filename": "Product.php", "line": "49"}, "connection": "shopping671", "start_percent": 80.154, "width_percent": 0.515}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 49}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755152861.122475, "duration": 0.00429, "duration_str": "4.29ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 80.67, "width_percent": 0.72}, {"sql": "select avg(`rating`) as aggregate from `reviews` where `reviews`.`product_id` = 22 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["22", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 49}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1755152861.129699, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Product.php:49", "source": "app/Models/Product.php:49", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=49", "ajax": false, "filename": "Product.php", "line": "49"}, "connection": "shopping671", "start_percent": 81.39, "width_percent": 0.086}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 49}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755152861.132234, "duration": 0.00433, "duration_str": "4.33ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 81.476, "width_percent": 0.727}, {"sql": "select avg(`rating`) as aggregate from `reviews` where `reviews`.`product_id` = 22 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["22", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 49}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1755152861.13915, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "Product.php:49", "source": "app/Models/Product.php:49", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=49", "ajax": false, "filename": "Product.php", "line": "49"}, "connection": "shopping671", "start_percent": 82.203, "width_percent": 0.131}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 49}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755152861.1428409, "duration": 0.0045899999999999995, "duration_str": "4.59ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 82.334, "width_percent": 0.771}, {"sql": "select avg(`rating`) as aggregate from `reviews` where `reviews`.`product_id` = 22 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["22", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 49}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1755152861.151443, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "Product.php:49", "source": "app/Models/Product.php:49", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=49", "ajax": false, "filename": "Product.php", "line": "49"}, "connection": "shopping671", "start_percent": 83.104, "width_percent": 0.148}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 660}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755152861.154631, "duration": 0.00631, "duration_str": "6.31ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 83.252, "width_percent": 1.06}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 22 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["22", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 660}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1755152861.163925, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Product.php:57", "source": "app/Models/Product.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=57", "ajax": false, "filename": "Product.php", "line": "57"}, "connection": "shopping671", "start_percent": 84.312, "width_percent": 0.094}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755152861.16767, "duration": 0.00433, "duration_str": "4.33ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 84.406, "width_percent": 0.727}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 37 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["37", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1755152861.174989, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "Product.php:57", "source": "app/Models/Product.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=57", "ajax": false, "filename": "Product.php", "line": "57"}, "connection": "shopping671", "start_percent": 85.133, "width_percent": 0.175}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755152861.178809, "duration": 0.00441, "duration_str": "4.41ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 85.307, "width_percent": 0.741}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 38 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["38", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1755152861.186301, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Product.php:57", "source": "app/Models/Product.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=57", "ajax": false, "filename": "Product.php", "line": "57"}, "connection": "shopping671", "start_percent": 86.048, "width_percent": 0.107}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755152861.190283, "duration": 0.00547, "duration_str": "5.47ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 86.155, "width_percent": 0.918}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 19 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["19", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1755152861.198452, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Product.php:57", "source": "app/Models/Product.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=57", "ajax": false, "filename": "Product.php", "line": "57"}, "connection": "shopping671", "start_percent": 87.074, "width_percent": 0.118}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 49}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755152861.201189, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 87.191, "width_percent": 0.67}, {"sql": "select avg(`rating`) as aggregate from `reviews` where `reviews`.`product_id` = 19 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["19", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 49}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1755152861.208155, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "Product.php:49", "source": "app/Models/Product.php:49", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=49", "ajax": false, "filename": "Product.php", "line": "49"}, "connection": "shopping671", "start_percent": 87.861, "width_percent": 0.148}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 49}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755152861.211677, "duration": 0.005030000000000001, "duration_str": "5.03ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 88.009, "width_percent": 0.845}, {"sql": "select avg(`rating`) as aggregate from `reviews` where `reviews`.`product_id` = 19 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["19", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 49}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1755152861.222487, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Product.php:49", "source": "app/Models/Product.php:49", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=49", "ajax": false, "filename": "Product.php", "line": "49"}, "connection": "shopping671", "start_percent": 88.854, "width_percent": 0.099}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 49}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755152861.225924, "duration": 0.00478, "duration_str": "4.78ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 88.953, "width_percent": 0.803}, {"sql": "select avg(`rating`) as aggregate from `reviews` where `reviews`.`product_id` = 19 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["19", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 49}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1755152861.233796, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Product.php:49", "source": "app/Models/Product.php:49", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=49", "ajax": false, "filename": "Product.php", "line": "49"}, "connection": "shopping671", "start_percent": 89.756, "width_percent": 0.123}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 49}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755152861.237538, "duration": 0.00752, "duration_str": "7.52ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 89.878, "width_percent": 1.263}, {"sql": "select avg(`rating`) as aggregate from `reviews` where `reviews`.`product_id` = 19 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["19", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 49}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1755152861.2492778, "duration": 0.0025099999999999996, "duration_str": "2.51ms", "memory": 0, "memory_str": null, "filename": "Product.php:49", "source": "app/Models/Product.php:49", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=49", "ajax": false, "filename": "Product.php", "line": "49"}, "connection": "shopping671", "start_percent": 91.141, "width_percent": 0.421}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 49}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755152861.2542129, "duration": 0.00547, "duration_str": "5.47ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 91.562, "width_percent": 0.918}, {"sql": "select avg(`rating`) as aggregate from `reviews` where `reviews`.`product_id` = 19 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["19", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 49}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 654}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1755152861.263134, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Product.php:49", "source": "app/Models/Product.php:49", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=49", "ajax": false, "filename": "Product.php", "line": "49"}, "connection": "shopping671", "start_percent": 92.481, "width_percent": 0.104}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 660}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755152861.266018, "duration": 0.009810000000000001, "duration_str": "9.81ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 92.585, "width_percent": 1.647}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 19 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["19", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 660}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1755152861.27837, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Product.php:57", "source": "app/Models/Product.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=57", "ajax": false, "filename": "Product.php", "line": "57"}, "connection": "shopping671", "start_percent": 94.232, "width_percent": 0.099}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755152861.281083, "duration": 0.00487, "duration_str": "4.87ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 94.331, "width_percent": 0.818}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 21 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["21", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1755152861.289438, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Product.php:57", "source": "app/Models/Product.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=57", "ajax": false, "filename": "Product.php", "line": "57"}, "connection": "shopping671", "start_percent": 95.149, "width_percent": 0.129}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755152861.293381, "duration": 0.004940000000000001, "duration_str": "4.94ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 95.278, "width_percent": 0.829}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 25 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["25", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1755152861.301221, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Product.php:57", "source": "app/Models/Product.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=57", "ajax": false, "filename": "Product.php", "line": "57"}, "connection": "shopping671", "start_percent": 96.108, "width_percent": 0.118}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755152861.3045058, "duration": 0.00833, "duration_str": "8.33ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 96.225, "width_percent": 1.399}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 47 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["47", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1755152861.3169749, "duration": 0.00266, "duration_str": "2.66ms", "memory": 0, "memory_str": null, "filename": "Product.php:57", "source": "app/Models/Product.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=57", "ajax": false, "filename": "Product.php", "line": "57"}, "connection": "shopping671", "start_percent": 97.624, "width_percent": 0.447}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755152861.32204, "duration": 0.00475, "duration_str": "4.75ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 98.071, "width_percent": 0.798}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 32 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["32", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1755152861.329804, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Product.php:57", "source": "app/Models/Product.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=57", "ajax": false, "filename": "Product.php", "line": "57"}, "connection": "shopping671", "start_percent": 98.868, "width_percent": 0.114}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'shopping671' and table_name = 'reviews'", "type": "query", "params": [], "bindings": ["shopping671", "reviews"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 35}, {"index": 13, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 19, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755152861.333368, "duration": 0.00523, "duration_str": "5.23ms", "memory": 0, "memory_str": null, "filename": "Product.php:35", "source": "app/Models/Product.php:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=35", "ajax": false, "filename": "Product.php", "line": "35"}, "connection": "shopping671", "start_percent": 98.982, "width_percent": 0.878}, {"sql": "select count(*) as aggregate from `reviews` where `reviews`.`product_id` = 33 and `reviews`.`product_id` is not null and `status` = 'approved'", "type": "query", "params": [], "bindings": ["33", "approved"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Models\\Product.php", "line": 57}, {"index": 24, "namespace": "view", "name": "index", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/index.blade.php", "line": 651}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": 1755152861.342947, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Product.php:57", "source": "app/Models/Product.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=57", "ajax": false, "filename": "Product.php", "line": "57"}, "connection": "shopping671", "start_percent": 99.861, "width_percent": 0.139}]}, "models": {"data": {"App\\Models\\Product": {"value": 92, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\Category": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\EventNews": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FEventNews.php&line=1", "ajax": false, "filename": "EventNews.php", "line": "?"}}, "App\\Models\\Brand": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 108, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "46ZnvqpIAX7jupoAGffRNJ359ZOTrdpNXkyPmPVi", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "4", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-207387148 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-207387148\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1016893160 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1016893160\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1615499797 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1615499797\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-956681784 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en,th-TH;q=0.9,th;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"540 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InVXM0NkOE9RYkVWUTdKSkh1Qm0zU2c9PSIsInZhbHVlIjoiRGdKSW8wOGQ5dkp2dGlpU2lDVGYrKy9lVndqb2wvMEVpT2FHWWpVcWpyWEJLa3NiTGN4L2FFaTM4U3V3ek9JbUtxRFZuZ1VqZlJ2cjUyNXZmSGF0Ryt1TGlPeXNNNW00dzlPSjc5VUtjRk5NT2RnRzhlbFdqaDBWNEFiOU9HcGdvUDE1U1MrODNyaTlhb0g2M1ZHQkI1M1VDeGpJR3hLcnN3OVA5TmlKTGJZYnlpT3BSSWx0WWhSU2t1UW01VmpjNkVxVThaOGZYR2ZocUtidUJuaW9idzkza3U2S2ZiYVRoZU41RE0rVWZYbz0iLCJtYWMiOiJjNDcyYjE3YmQ2M2E5NjM0ZDY4NGZmZjcyYmY2N2YwMTQ3OTRkYjgxYzMyODRlYjM0Mjk0NjgzNzU5NTYxYTkzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-956681784\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1276243626 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">4|eReTiTBqKqjOVPfFvFHTfu5nP0cX8wNN1AHXPHDYTrVv9QJ4mKXvJoQfFdGB|$2y$10$.atQ6R7qWeH0KVSPlkrFfeKwTkG9nLA0QuYOorGPJGaHEfKXSF5Aq</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1276243626\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1457031134 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 14 Aug 2025 06:27:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InhxYitsQXlCRTkyVU9ZUzg0K3NRV2c9PSIsInZhbHVlIjoiMUE5eTZDVEhHczNVZG5XZ0RQOHFMV1RvVXV2V25KZkVvQjNWTFZaWERoTWtoSHl5MWszSU1BK0hjU2R4YlZuM1RjeElWWUxJK0VIRERCTFd3VXRYQytYNURRSlBQKzY3b05zYkV4dzJ4dW5ZdHVGWEZMdGZaQVYzOE4rQVBsOW0iLCJtYWMiOiIzZTJlYmJlYWM3NDYyYzc3YTQyN2RjZmEyNTY3Mzc2ZDU1YjRlZDI5NjljMWY1YTY4OGQyN2NhMGI0ZTViMmQzIiwidGFnIjoiIn0%3D; expires=Thu, 14 Aug 2025 08:27:41 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImZPUTRHRXZEVjFCV0xhcE5wYXFHZnc9PSIsInZhbHVlIjoidnpFVm9nUkNzNWROUy90dkNpWGZYdnhER00zdCsyYlorempCZ0dyUm81WmhuYVlaWlJTOUsvU1hWeEpZQnQ3WU5wWWRGczBGeDl0OTRlbzRrY1l4RXFaL0hvemhuU2tXQ2hZQ01hcjdReFhGV2k0WWpHbUtpbE1adkhvaWE3N1AiLCJtYWMiOiIzNjdmYjExN2Q3YzUyOTQyMzJiMTBhMWU0ODE4NTI5YTNmNzc4ODQ5MjQ4YmQ5YTFlMzgwNGQ4ZjQ2NWM4MDY2IiwidGFnIjoiIn0%3D; expires=Thu, 14 Aug 2025 08:27:41 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InhxYitsQXlCRTkyVU9ZUzg0K3NRV2c9PSIsInZhbHVlIjoiMUE5eTZDVEhHczNVZG5XZ0RQOHFMV1RvVXV2V25KZkVvQjNWTFZaWERoTWtoSHl5MWszSU1BK0hjU2R4YlZuM1RjeElWWUxJK0VIRERCTFd3VXRYQytYNURRSlBQKzY3b05zYkV4dzJ4dW5ZdHVGWEZMdGZaQVYzOE4rQVBsOW0iLCJtYWMiOiIzZTJlYmJlYWM3NDYyYzc3YTQyN2RjZmEyNTY3Mzc2ZDU1YjRlZDI5NjljMWY1YTY4OGQyN2NhMGI0ZTViMmQzIiwidGFnIjoiIn0%3D; expires=Thu, 14-Aug-2025 08:27:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImZPUTRHRXZEVjFCV0xhcE5wYXFHZnc9PSIsInZhbHVlIjoidnpFVm9nUkNzNWROUy90dkNpWGZYdnhER00zdCsyYlorempCZ0dyUm81WmhuYVlaWlJTOUsvU1hWeEpZQnQ3WU5wWWRGczBGeDl0OTRlbzRrY1l4RXFaL0hvemhuU2tXQ2hZQ01hcjdReFhGV2k0WWpHbUtpbE1adkhvaWE3N1AiLCJtYWMiOiIzNjdmYjExN2Q3YzUyOTQyMzJiMTBhMWU0ODE4NTI5YTNmNzc4ODQ5MjQ4YmQ5YTFlMzgwNGQ4ZjQ2NWM4MDY2IiwidGFnIjoiIn0%3D; expires=Thu, 14-Aug-2025 08:27:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1457031134\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1943349774 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">46ZnvqpIAX7jupoAGffRNJ359ZOTrdpNXkyPmPVi</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>4</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1943349774\", {\"maxDepth\":0})</script>\n"}}