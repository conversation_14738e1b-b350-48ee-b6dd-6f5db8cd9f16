{"__meta": {"id": "X0648a909bdc591c56b7fbdb06a53592f", "datetime": "2025-08-14 13:29:53", "utime": 1755152993.058452, "method": "GET", "uri": "/all/cate", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.586401, "end": 1755152993.058478, "duration": 0.4720771312713623, "duration_str": "472ms", "measures": [{"label": "Booting", "start": **********.586401, "relative_start": 0, "end": **********.732103, "relative_end": **********.732103, "duration": 0.14570212364196777, "duration_str": "146ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.732128, "relative_start": 0.14572691917419434, "end": 1755152993.05848, "relative_end": 1.9073486328125e-06, "duration": 0.3263521194458008, "duration_str": "326ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 20996992, "peak_usage_str": "20MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 7, "templates": [{"name": "admin.categories", "param_count": null, "params": [], "start": **********.835692, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/admin/categories.blade.phpadmin.categories", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fcategories.blade.php&line=1", "ajax": false, "filename": "categories.blade.php", "line": "?"}}, {"name": "pagination::bootstrap-4", "param_count": null, "params": [], "start": **********.935842, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination/resources/views/bootstrap-4.blade.phppagination::bootstrap-4", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FPagination%2Fresources%2Fviews%2Fbootstrap-4.blade.php&line=1", "ajax": false, "filename": "bootstrap-4.blade.php", "line": "?"}}, {"name": "admin.admin_master", "param_count": null, "params": [], "start": 1755152993.038595, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/admin/admin_master.blade.phpadmin.admin_master", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fadmin_master.blade.php&line=1", "ajax": false, "filename": "admin_master.blade.php", "line": "?"}}, {"name": "sweetalert::alert", "param_count": null, "params": [], "start": 1755152993.039498, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/vendor/sweetalert/alert.blade.phpsweetalert::alert", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fvendor%2Fsweetalert%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}}, {"name": "admin.body.header", "param_count": null, "params": [], "start": 1755152993.040274, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/admin/body/header.blade.phpadmin.body.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fbody%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "admin.body.slidemenu", "param_count": null, "params": [], "start": 1755152993.043767, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/admin/body/slidemenu.blade.phpadmin.body.slidemenu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fbody%2Fslidemenu.blade.php&line=1", "ajax": false, "filename": "slidemenu.blade.php", "line": "?"}}, {"name": "admin.body.footer", "param_count": null, "params": [], "start": 1755152993.044557, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\shopping67\\resources\\views/admin/body/footer.blade.phpadmin.body.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fbody%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET all/cate", "middleware": "web, auth, admin, Closure", "controller": "App\\Http\\Controllers\\CategoryController@index", "namespace": null, "prefix": "", "where": [], "as": "category.index", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FCategoryController.php&line=20\" onclick=\"\">app/Http/Controllers/CategoryController.php:20-27</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00455, "accumulated_duration_str": "4.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 15, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.798796, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "shopping671", "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.8097, "duration": 0.00249, "duration_str": "2.49ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "shopping671", "start_percent": 0, "width_percent": 54.725}, {"sql": "select count(*) as aggregate from `categories`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/CategoryController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\CategoryController.php", "line": 24}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.817048, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:24", "source": "app/Http/Controllers/CategoryController.php:24", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FCategoryController.php&line=24", "ajax": false, "filename": "CategoryController.php", "line": "24"}, "connection": "shopping671", "start_percent": 54.725, "width_percent": 9.89}, {"sql": "select * from `categories` limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/CategoryController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\CategoryController.php", "line": 24}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.8196201, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:24", "source": "app/Http/Controllers/CategoryController.php:24", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FCategoryController.php&line=24", "ajax": false, "filename": "CategoryController.php", "line": "24"}, "connection": "shopping671", "start_percent": 64.615, "width_percent": 13.626}, {"sql": "select count(*) as aggregate from `categories`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/CategoryController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\CategoryController.php", "line": 25}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.8248532, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "CategoryController.php:25", "source": "app/Http/Controllers/CategoryController.php:25", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FCategoryController.php&line=25", "ajax": false, "filename": "CategoryController.php", "line": "25"}, "connection": "shopping671", "start_percent": 78.242, "width_percent": 9.67}, {"sql": "select * from `users` where `users`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "admin.body.header", "file": "C:\\xampp\\htdocs\\shopping67\\resources\\views/admin/body/header.blade.php", "line": 32}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": 1755152993.0409029, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "admin.body.header:32", "source": "view::admin.body.header:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fresources%2Fviews%2Fadmin%2Fbody%2Fheader.blade.php&line=32", "ajax": false, "filename": "header.blade.php", "line": "32"}, "connection": "shopping671", "start_percent": 87.912, "width_percent": 12.088}]}, "models": {"data": {"App\\Models\\Category": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "I2Z3Vz3xluQ63a2C2hMQ319YZoVohQYC46c3D2Ar", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/all/cate\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/all/cate", "status_code": "<pre class=sf-dump id=sf-dump-36884247 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-36884247\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1606212121 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1606212121\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-475423505 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-475423505\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-633217118 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/products/all</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en,th-TH;q=0.9,th;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Im5TL3g3VjhHcUtEazBUdk84RTZnYlE9PSIsInZhbHVlIjoidFpUMlJYVGxCbHcyREdmZ25zMkszblhib3Nod05jSUkreVdENjBIdzhBaW5HL3ZYM1pHUVMxbEc0Qzh3OVZuQzhDU3BOSXRCRmEzSVRVVVZyTjBMUUdZRlFBQWVXTjRhQmhGUnZKcEVHakFIdXpIVVVOTDgvWFprV1Y2eXdoU0ciLCJtYWMiOiJjYzBlYjg4MTMxYjAzZmMzMWExMTMwMWI2MTNkOTdmN2M0MzBiN2U3MmZhNmI1MzRmZjYwYTc1YmZjNjJjNTI3IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlZTMUR0V1RLTFhKOUtiL0xIK1FDeHc9PSIsInZhbHVlIjoidXcwd0c4T3VBSnRrVk8reWlNaGE4RlYrSzZ1QjYrS3lVN1I0djJFQktYV0tMcW1Td1JUQkNLRlArY1lEZkRtQkFQVnFpZUxMb3czWXlKYndScFkvaDNZU3ZWTEcxbTRCSzdGSFVrQ2lZVW5JM1NMWWFoV3lxV0JaT2Nha3ZFYkIiLCJtYWMiOiIwMjRlMjYzZjE3YzIwNDdkYjY4ZDE2NzllZDA1ZjA0Mzk2ZDY2NDU4NzdlZmFmMzFiOTZiZDljYTQ2ZWUwZjExIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-633217118\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-247826442 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I2Z3Vz3xluQ63a2C2hMQ319YZoVohQYC46c3D2Ar</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ChTsjbIyZRcG6kRqcd2vjnKzNyQaAU2IaFy0VSoa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-247826442\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-699443507 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 14 Aug 2025 06:29:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InBjWis0VG1KMzBTdWp0eFU3YzBKaHc9PSIsInZhbHVlIjoiWTZ4a1l6NytZbGtaOUloRWNzQlM3TzF6MXIwWEgvVnNiR2pHd0ZjUGx1VEVsRWd4dkJiOFNhakxrVkxWdEhkc0JpQjhtbG4zT2hpZ04wZ2RUZysvSy9kd0VMdG5PdGUvbnk5ZXFjczYvTVFMRHJ0T1hvZEp0VUxnclJ3YWh4MC8iLCJtYWMiOiJkMDU4ZjczYzM2ZjU5MWY1MmEwNzEyZjVkNjY4OTAyYmViMjExODZmZjUyYjExMjgzYmI4NjAxNjM1ODdiNmVhIiwidGFnIjoiIn0%3D; expires=Thu, 14 Aug 2025 08:29:53 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IncwN1oyMGF3bklkSEY1M0U4QkRJalE9PSIsInZhbHVlIjoiVWxsRG54ZDZTdEh2WjF1M2kyN1I5TXR4S0JBVG5DMWtEU3Evb25IVlVrZGEzMFdtS3hqOWZra1ZOTW5FN3N4dGttZzlHSGtnMDlvZnAwM1VjbTIra3N5SDhQUDRZVkZqYmFNdlg3MllxQ29pWFhCL2xySG4vc045UHpGMjNiV0UiLCJtYWMiOiI1ZjJiZGZmMWQyYzQzOTJiMDVlMjA3YTIyZGIyNThlZGEwYjBmOWQ5MzMwNzZkNzE3ZTNiNzM4M2Y4ZDJhZjFmIiwidGFnIjoiIn0%3D; expires=Thu, 14 Aug 2025 08:29:53 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InBjWis0VG1KMzBTdWp0eFU3YzBKaHc9PSIsInZhbHVlIjoiWTZ4a1l6NytZbGtaOUloRWNzQlM3TzF6MXIwWEgvVnNiR2pHd0ZjUGx1VEVsRWd4dkJiOFNhakxrVkxWdEhkc0JpQjhtbG4zT2hpZ04wZ2RUZysvSy9kd0VMdG5PdGUvbnk5ZXFjczYvTVFMRHJ0T1hvZEp0VUxnclJ3YWh4MC8iLCJtYWMiOiJkMDU4ZjczYzM2ZjU5MWY1MmEwNzEyZjVkNjY4OTAyYmViMjExODZmZjUyYjExMjgzYmI4NjAxNjM1ODdiNmVhIiwidGFnIjoiIn0%3D; expires=Thu, 14-Aug-2025 08:29:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IncwN1oyMGF3bklkSEY1M0U4QkRJalE9PSIsInZhbHVlIjoiVWxsRG54ZDZTdEh2WjF1M2kyN1I5TXR4S0JBVG5DMWtEU3Evb25IVlVrZGEzMFdtS3hqOWZra1ZOTW5FN3N4dGttZzlHSGtnMDlvZnAwM1VjbTIra3N5SDhQUDRZVkZqYmFNdlg3MllxQ29pWFhCL2xySG4vc045UHpGMjNiV0UiLCJtYWMiOiI1ZjJiZGZmMWQyYzQzOTJiMDVlMjA3YTIyZGIyNThlZGEwYjBmOWQ5MzMwNzZkNzE3ZTNiNzM4M2Y4ZDJhZjFmIiwidGFnIjoiIn0%3D; expires=Thu, 14-Aug-2025 08:29:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-699443507\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1701948660 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">I2Z3Vz3xluQ63a2C2hMQ319YZoVohQYC46c3D2Ar</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/all/cate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1701948660\", {\"maxDepth\":0})</script>\n"}}