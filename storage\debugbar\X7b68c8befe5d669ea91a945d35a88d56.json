{"__meta": {"id": "X7b68c8befe5d669ea91a945d35a88d56", "datetime": "2025-08-14 13:27:55", "utime": **********.221011, "method": "POST", "uri": "/logout", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1755152874.70062, "end": **********.22104, "duration": 0.5204200744628906, "duration_str": "520ms", "measures": [{"label": "Booting", "start": 1755152874.70062, "relative_start": 0, "end": 1755152874.864657, "relative_end": 1755152874.864657, "duration": 0.16403698921203613, "duration_str": "164ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1755152874.864673, "relative_start": 0.16405296325683594, "end": **********.221043, "relative_end": 3.0994415283203125e-06, "duration": 0.356370210647583, "duration_str": "356ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 20343416, "peak_usage_str": "19MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST logout", "middleware": "web, auth, Closure", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@destroy", "namespace": null, "prefix": "", "where": [], "as": "logout", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=45\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:45-54</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00797, "accumulated_duration_str": "7.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 168}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 57}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 15, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.025853, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:168", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=168", "ajax": false, "filename": "EloquentUserProvider.php", "line": "168"}, "connection": "shopping671", "start_percent": 0, "width_percent": 0}, {"sql": "select * from `users` where `id` = 4 limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.036718, "duration": 0.00254, "duration_str": "2.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "shopping671", "start_percent": 0, "width_percent": 31.87}, {"sql": "update `users` set `remember_token` = 'ykVounitiUq3AKf8RtDeBnEfcTgfMBiDbBXm0LxGkPOOr0J7htQonr7UbVmM' where `id` = 4", "type": "query", "params": [], "bindings": ["ykVounitiUq3AKf8RtDeBnEfcTgfMBiDbBXm0LxGkPOOr0J7htQonr7UbVmM", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 101}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 650}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 576}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\xampp\\htdocs\\shopping67\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\shopping67\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.066049, "duration": 0.00543, "duration_str": "5.43ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:101", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:101", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=101", "ajax": false, "filename": "EloquentUserProvider.php", "line": "101"}, "connection": "shopping671", "start_percent": 31.87, "width_percent": 68.13}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fshopping67%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "IX4eeRzHVAg2fDHcVCKcCYviHDYT5eo7NDYwVkKu", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/logout", "status_code": "<pre class=sf-dump id=sf-dump-907520527 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-907520527\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1933458755 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">46ZnvqpIAX7jupoAGffRNJ359ZOTrdpNXkyPmPVi</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1933458755\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1704906975 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en,th-TH;q=0.9,th;q=0.8,en-US;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1255 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InVXM0NkOE9RYkVWUTdKSkh1Qm0zU2c9PSIsInZhbHVlIjoiRGdKSW8wOGQ5dkp2dGlpU2lDVGYrKy9lVndqb2wvMEVpT2FHWWpVcWpyWEJLa3NiTGN4L2FFaTM4U3V3ek9JbUtxRFZuZ1VqZlJ2cjUyNXZmSGF0Ryt1TGlPeXNNNW00dzlPSjc5VUtjRk5NT2RnRzhlbFdqaDBWNEFiOU9HcGdvUDE1U1MrODNyaTlhb0g2M1ZHQkI1M1VDeGpJR3hLcnN3OVA5TmlKTGJZYnlpT3BSSWx0WWhSU2t1UW01VmpjNkVxVThaOGZYR2ZocUtidUJuaW9idzkza3U2S2ZiYVRoZU41RE0rVWZYbz0iLCJtYWMiOiJjNDcyYjE3YmQ2M2E5NjM0ZDY4NGZmZjcyYmY2N2YwMTQ3OTRkYjgxYzMyODRlYjM0Mjk0NjgzNzU5NTYxYTkzIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InhxYitsQXlCRTkyVU9ZUzg0K3NRV2c9PSIsInZhbHVlIjoiMUE5eTZDVEhHczNVZG5XZ0RQOHFMV1RvVXV2V25KZkVvQjNWTFZaWERoTWtoSHl5MWszSU1BK0hjU2R4YlZuM1RjeElWWUxJK0VIRERCTFd3VXRYQytYNURRSlBQKzY3b05zYkV4dzJ4dW5ZdHVGWEZMdGZaQVYzOE4rQVBsOW0iLCJtYWMiOiIzZTJlYmJlYWM3NDYyYzc3YTQyN2RjZmEyNTY3Mzc2ZDU1YjRlZDI5NjljMWY1YTY4OGQyN2NhMGI0ZTViMmQzIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImZPUTRHRXZEVjFCV0xhcE5wYXFHZnc9PSIsInZhbHVlIjoidnpFVm9nUkNzNWROUy90dkNpWGZYdnhER00zdCsyYlorempCZ0dyUm81WmhuYVlaWlJTOUsvU1hWeEpZQnQ3WU5wWWRGczBGeDl0OTRlbzRrY1l4RXFaL0hvemhuU2tXQ2hZQ01hcjdReFhGV2k0WWpHbUtpbE1adkhvaWE3N1AiLCJtYWMiOiIzNjdmYjExN2Q3YzUyOTQyMzJiMTBhMWU0ODE4NTI5YTNmNzc4ODQ5MjQ4YmQ5YTFlMzgwNGQ4ZjQ2NWM4MDY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1704906975\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-39452811 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">4|eReTiTBqKqjOVPfFvFHTfu5nP0cX8wNN1AHXPHDYTrVv9QJ4mKXvJoQfFdGB|$2y$10$.atQ6R7qWeH0KVSPlkrFfeKwTkG9nLA0QuYOorGPJGaHEfKXSF5Aq</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">46ZnvqpIAX7jupoAGffRNJ359ZOTrdpNXkyPmPVi</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">X7hQJhF9cunUa3dFDHyWYs44j3ITsvSamM48vYn6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-39452811\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1798240318 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 14 Aug 2025 06:27:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ik5LTTBxRWU0QzFKcWQyY05FazcrOVE9PSIsInZhbHVlIjoic0RCNzdRQXc5eUhSc3c3QUxubG1odDBEbVVLdVdpcUlQbThDZzhueW1EbzM4TEk2L3ZvVHZNTGV0cWNWYkFodjVraUs1eWJTYVdLSVNYR1BtSTN3QjRDbkpwWmZDc1JqUnk2SS9xYzNsZko0dlRSbS9heHgxWGhTOUd0Slg3TkIiLCJtYWMiOiI5YmViYzg1ODk0Y2U2NjQxMWVmMGFhZDMxNTkyZWYxZTcwZjg3ODBlNThiNDE5MGRlMDQ0MzYxYTIzZTE1NGI3IiwidGFnIjoiIn0%3D; expires=Thu, 14 Aug 2025 08:27:55 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6ImN6am96bkN2TCtxUVZNK0w2aVVoeXc9PSIsInZhbHVlIjoiRUh4ZVZpSzFKekRFY2JseCtraTFZOGhtVDkydHhnRjFNbCtmVjBBa21nSzhRN0pzQmVQL3NLdFNRcjNQTkpsY2o5eXdrb2t3U05yL1cxeC8vc3d5czZ3L0FuR0lRYStkZ09mNWFKMjlNckd5WHcyME53akNVWHFEWU5OUHpLV1UiLCJtYWMiOiJhNjJjODk2YWFkNzE1MTJmOTVhYzA3N2E5ZDM0ODNiOTg5YjNkZWZkOTA2OGU1OWNhN2M0MzEyMmZjMmIzMzY1IiwidGFnIjoiIn0%3D; expires=Thu, 14 Aug 2025 08:27:55 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"396 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImhyY3o0QnkyeHZoY3A3d25wZDl2Y3c9PSIsInZhbHVlIjoidnUzWTBFVnJ2bXYyME53M3RyVk95VWZpNkVOMkgzblhDMCtoM1RodEJpc1BMYXh2SEFsbWFseFRnMjB5RysxbyIsIm1hYyI6IjZmOTUwODdkMGI5MjFlZWVjMzIzMTVjMDEwNGYwN2M5YmJlOWQwMzBmY2YzNGQ2ZmI1YTNjNzNhODZjNzdmMjEiLCJ0YWciOiIifQ%3D%3D; expires=Sat, 15 Aug 2020 06:27:55 GMT; Max-Age=0; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ik5LTTBxRWU0QzFKcWQyY05FazcrOVE9PSIsInZhbHVlIjoic0RCNzdRQXc5eUhSc3c3QUxubG1odDBEbVVLdVdpcUlQbThDZzhueW1EbzM4TEk2L3ZvVHZNTGV0cWNWYkFodjVraUs1eWJTYVdLSVNYR1BtSTN3QjRDbkpwWmZDc1JqUnk2SS9xYzNsZko0dlRSbS9heHgxWGhTOUd0Slg3TkIiLCJtYWMiOiI5YmViYzg1ODk0Y2U2NjQxMWVmMGFhZDMxNTkyZWYxZTcwZjg3ODBlNThiNDE5MGRlMDQ0MzYxYTIzZTE1NGI3IiwidGFnIjoiIn0%3D; expires=Thu, 14-Aug-2025 08:27:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6ImN6am96bkN2TCtxUVZNK0w2aVVoeXc9PSIsInZhbHVlIjoiRUh4ZVZpSzFKekRFY2JseCtraTFZOGhtVDkydHhnRjFNbCtmVjBBa21nSzhRN0pzQmVQL3NLdFNRcjNQTkpsY2o5eXdrb2t3U05yL1cxeC8vc3d5czZ3L0FuR0lRYStkZ09mNWFKMjlNckd5WHcyME53akNVWHFEWU5OUHpLV1UiLCJtYWMiOiJhNjJjODk2YWFkNzE1MTJmOTVhYzA3N2E5ZDM0ODNiOTg5YjNkZWZkOTA2OGU1OWNhN2M0MzEyMmZjMmIzMzY1IiwidGFnIjoiIn0%3D; expires=Thu, 14-Aug-2025 08:27:55 GMT; path=/; httponly</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"371 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImhyY3o0QnkyeHZoY3A3d25wZDl2Y3c9PSIsInZhbHVlIjoidnUzWTBFVnJ2bXYyME53M3RyVk95VWZpNkVOMkgzblhDMCtoM1RodEJpc1BMYXh2SEFsbWFseFRnMjB5RysxbyIsIm1hYyI6IjZmOTUwODdkMGI5MjFlZWVjMzIzMTVjMDEwNGYwN2M5YmJlOWQwMzBmY2YzNGQ2ZmI1YTNjNzNhODZjNzdmMjEiLCJ0YWciOiIifQ%3D%3D; expires=Sat, 15-Aug-2020 06:27:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1798240318\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-978337062 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IX4eeRzHVAg2fDHcVCKcCYviHDYT5eo7NDYwVkKu</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-978337062\", {\"maxDepth\":0})</script>\n"}}